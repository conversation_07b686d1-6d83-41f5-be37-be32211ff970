<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewRole extends ViewRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Role Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('display_name')
                            ->label('Display Name'),
                        Infolists\Components\TextEntry::make('name')
                            ->label('System Name')
                            ->fontFamily('mono'),
                        Infolists\Components\IconEntry::make('is_default')
                            ->label('Default Role')
                            ->boolean(),
                        Infolists\Components\TextEntry::make('guard_name')
                            ->label('Guard'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Permissions')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('permissions')
                            ->label('Assigned Permissions')
                            ->schema([
                                Infolists\Components\TextEntry::make('display_name')
                                    ->label('Permission'),
                                Infolists\Components\TextEntry::make('name')
                                    ->label('System Name')
                                    ->fontFamily('mono')
                                    ->color('gray'),
                            ])
                            ->columns(2)
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Users with this Role')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('users')
                            ->label('Assigned Users')
                            ->schema([
                                Infolists\Components\TextEntry::make('full_name')
                                    ->label('Name'),
                                Infolists\Components\TextEntry::make('email')
                                    ->label('Email'),
                            ])
                            ->columns(2)
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Metadata')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
