<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Define permissions with categories
        $permissions = [
            // Invoice Management
            'invoices' => [
                'view_invoices' => 'View Invoices',
                'create_invoices' => 'Create Invoices',
                'edit_invoices' => 'Edit Invoices',
                'delete_invoices' => 'Delete Invoices',
                'send_invoices' => 'Send Invoices',
                'update_invoice_status' => 'Update Invoice Status',
                'view_invoice_payments' => 'View Invoice Payments',
            ],

            // Client Management
            'clients' => [
                'view_clients' => 'View Clients',
                'create_clients' => 'Create Clients',
                'edit_clients' => 'Edit Clients',
                'delete_clients' => 'Delete Clients',
                'view_client_invoices' => 'View Client Invoices',
            ],

            // Payment Management
            'payments' => [
                'view_payments' => 'View Payments',
                'create_payments' => 'Create Payments',
                'edit_payments' => 'Edit Payments',
                'delete_payments' => 'Delete Payments',
                'approve_payments' => 'Approve Payments',
                'reject_payments' => 'Reject Payments',
            ],

            // Product Management
            'products' => [
                'view_products' => 'View Products',
                'create_products' => 'Create Products',
                'edit_products' => 'Edit Products',
                'delete_products' => 'Delete Products',
            ],

            // User Management
            'users' => [
                'view_users' => 'View Users',
                'create_users' => 'Create Users',
                'edit_users' => 'Edit Users',
                'delete_users' => 'Delete Users',
                'manage_user_roles' => 'Manage User Roles',
            ],

            // Role & Permission Management
            'roles' => [
                'view_roles' => 'View Roles',
                'create_roles' => 'Create Roles',
                'edit_roles' => 'Edit Roles',
                'delete_roles' => 'Delete Roles',
                'assign_permissions' => 'Assign Permissions',
            ],

            // Settings Management
            'settings' => [
                'view_settings' => 'View Settings',
                'edit_settings' => 'Edit Settings',
                'manage_templates' => 'Manage Templates',
                'manage_currencies' => 'Manage Currencies',
                'manage_taxes' => 'Manage Taxes',
            ],

            // Reports & Analytics
            'reports' => [
                'view_reports' => 'View Reports',
                'export_reports' => 'Export Reports',
                'view_analytics' => 'View Analytics',
            ],

            // Logistics (Future Enhancement)
            'logistics' => [
                'view_shipments' => 'View Shipments',
                'create_shipments' => 'Create Shipments',
                'edit_shipments' => 'Edit Shipments',
                'track_shipments' => 'Track Shipments',
                'manage_inventory' => 'Manage Inventory',
            ],
        ];

        // Create permissions
        foreach ($permissions as $category => $categoryPermissions) {
            foreach ($categoryPermissions as $name => $displayName) {
                Permission::firstOrCreate([
                    'name' => $name,
                    'guard_name' => 'web',
                ], [
                    'display_name' => $displayName,
                    'category' => $category,
                ]);
            }
        }

        // Create or update roles with permissions
        $this->createAdminRole($permissions);
        $this->createClientRole();
        $this->createManagerRole($permissions);
        $this->createAccountantRole($permissions);
    }

    private function createAdminRole(array $permissions): void
    {
        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'web',
        ], [
            'display_name' => 'Administrator',
            'is_default' => false,
        ]);

        // Admin gets all permissions
        $allPermissions = [];
        foreach ($permissions as $categoryPermissions) {
            $allPermissions = array_merge($allPermissions, array_keys($categoryPermissions));
        }

        $adminRole->syncPermissions($allPermissions);
    }

    private function createClientRole(): void
    {
        $clientRole = Role::firstOrCreate([
            'name' => 'client',
            'guard_name' => 'web',
        ], [
            'display_name' => 'Client',
            'is_default' => true,
        ]);

        // Client gets limited permissions
        $clientPermissions = [
            'view_invoices',
            'view_invoice_payments',
            'create_payments',
        ];

        $clientRole->syncPermissions($clientPermissions);
    }

    private function createManagerRole(array $permissions): void
    {
        $managerRole = Role::firstOrCreate([
            'name' => 'manager',
            'guard_name' => 'web',
        ], [
            'display_name' => 'Manager',
            'is_default' => false,
        ]);

        // Manager gets most permissions except user/role management
        $managerPermissions = [];
        foreach ($permissions as $category => $categoryPermissions) {
            if (!in_array($category, ['users', 'roles'])) {
                $managerPermissions = array_merge($managerPermissions, array_keys($categoryPermissions));
            }
        }

        $managerRole->syncPermissions($managerPermissions);
    }

    private function createAccountantRole(array $permissions): void
    {
        $accountantRole = Role::firstOrCreate([
            'name' => 'accountant',
            'guard_name' => 'web',
        ], [
            'display_name' => 'Accountant',
            'is_default' => false,
        ]);

        // Accountant gets financial-related permissions
        $accountantPermissions = [
            'view_invoices', 'create_invoices', 'edit_invoices', 'send_invoices', 'update_invoice_status',
            'view_invoice_payments', 'view_payments', 'create_payments', 'edit_payments', 'approve_payments',
            'view_clients', 'create_clients', 'edit_clients',
            'view_products', 'create_products', 'edit_products',
            'view_reports', 'export_reports', 'view_analytics',
        ];

        $accountantRole->syncPermissions($accountantPermissions);
    }
}
