const Ziggy = {"url":"http:\/\/invoice-filament.test","port":null,"defaults":{},"routes":{"filament.exports.download":{"uri":"filament\/exports\/{export}\/download","methods":["GET","HEAD"],"parameters":["export"],"bindings":{"export":"id"}},"filament.imports.failed-rows.download":{"uri":"filament\/imports\/{import}\/failed-rows\/download","methods":["GET","HEAD"],"parameters":["import"],"bindings":{"import":"id"}},"filament.admin.auth.login":{"uri":"login","methods":["GET","HEAD"]},"filament.admin.auth.logout":{"uri":"logout","methods":["POST"]},"filament.admin.auth.profile":{"uri":"profile","methods":["GET","HEAD"]},"filament.admin.pages.dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"filament.admin.pages.invoice-templates":{"uri":"invoice-templates","methods":["GET","HEAD"]},"filament.admin.pages.currency-report":{"uri":"currency-report","methods":["GET","HEAD"]},"filament.admin.countries":{"uri":"countries","methods":["GET","HEAD"]},"filament.admin.settings":{"uri":"settings","methods":["GET","HEAD"]},"filament.admin.settings.pages.general":{"uri":"settings\/general","methods":["GET","HEAD"]},"filament.admin.settings.pages.invoice-settings":{"uri":"settings\/invoice-settings","methods":["GET","HEAD"]},"filament.admin.settings.pages.payment-gateway":{"uri":"settings\/payment-gateway","methods":["GET","HEAD"]},"filament.admin.resources.admins.index":{"uri":"admins","methods":["GET","HEAD"]},"filament.admin.resources.admins.create":{"uri":"admins\/create","methods":["GET","HEAD"]},"filament.admin.resources.admins.view":{"uri":"admins\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.admins.edit":{"uri":"admins\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.categories.index":{"uri":"categories","methods":["GET","HEAD"]},"filament.admin.resources.clients.index":{"uri":"clients","methods":["GET","HEAD"]},"filament.admin.resources.clients.create":{"uri":"clients\/create","methods":["GET","HEAD"]},"filament.admin.resources.clients.view":{"uri":"clients\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.clients.edit":{"uri":"clients\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.payment-qr-codes.index":{"uri":"payment-qr-codes","methods":["GET","HEAD"]},"filament.admin.resources.payments.index":{"uri":"payments","methods":["GET","HEAD"]},"filament.admin.resources.products.index":{"uri":"products","methods":["GET","HEAD"]},"filament.admin.resources.products.create":{"uri":"products\/create","methods":["GET","HEAD"]},"filament.admin.resources.products.edit":{"uri":"products\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.taxes.index":{"uri":"taxes","methods":["GET","HEAD"]},"filament.admin.resources.invoices.index":{"uri":"invoices","methods":["GET","HEAD"]},"filament.admin.resources.invoices.create":{"uri":"invoices\/create","methods":["GET","HEAD"]},"filament.admin.resources.invoices.view":{"uri":"invoices\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.invoices.edit":{"uri":"invoices\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.quotes.index":{"uri":"quotes","methods":["GET","HEAD"]},"filament.admin.resources.quotes.create":{"uri":"quotes\/create","methods":["GET","HEAD"]},"filament.admin.resources.quotes.view":{"uri":"quotes\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.quotes.edit":{"uri":"quotes\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.transactions.index":{"uri":"transactions","methods":["GET","HEAD"]},"filament.admin.countries.resources.cities.index":{"uri":"countries\/cities","methods":["GET","HEAD"]},"filament.admin.countries.resources.countries.index":{"uri":"countries\/countries","methods":["GET","HEAD"]},"filament.admin.countries.resources.states.index":{"uri":"countries\/states","methods":["GET","HEAD"]},"filament.admin.settings.resources.currencies.index":{"uri":"settings\/currencies","methods":["GET","HEAD"]},"filament.client.auth.logout":{"uri":"client\/logout","methods":["POST"]},"filament.client.pages.dashboard":{"uri":"client","methods":["GET","HEAD"]},"filament.client.pages.currency-report":{"uri":"client\/currency-report","methods":["GET","HEAD"]},"filament.client.resources.invoices.index":{"uri":"client\/invoices","methods":["GET","HEAD"]},"filament.client.resources.invoices.create":{"uri":"client\/invoices\/create","methods":["GET","HEAD"]},"filament.client.resources.invoices.view":{"uri":"client\/invoices\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.client.resources.invoices.edit":{"uri":"client\/invoices\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.client.resources.quotes.index":{"uri":"client\/quotes","methods":["GET","HEAD"]},"filament.client.resources.quotes.create":{"uri":"client\/quotes\/create","methods":["GET","HEAD"]},"filament.client.resources.quotes.view":{"uri":"client\/quotes\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.client.resources.quotes.edit":{"uri":"client\/quotes\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.client.resources.transactions.index":{"uri":"client\/transactions","methods":["GET","HEAD"]},"livewire.update":{"uri":"livewire\/update","methods":["POST"]},"livewire.upload-file":{"uri":"livewire\/upload-file","methods":["POST"]},"livewire.preview-file":{"uri":"livewire\/preview-file\/{filename}","methods":["GET","HEAD"],"parameters":["filename"]},"quote-show-url":{"uri":"quote\/{quoteId}","methods":["GET","HEAD"],"parameters":["quoteId"]},"invoice-show-url":{"uri":"invoice\/{invoiceId}","methods":["GET","HEAD"],"parameters":["invoiceId"]},"public-view-quote.pdf":{"uri":"quote-pdf\/{quote}","methods":["GET","HEAD"],"parameters":["quote"]},"invoices.pdf":{"uri":"invoices\/{invoice}\/pdf","methods":["GET","HEAD"],"parameters":["invoice"],"bindings":{"invoice":"id"}},"invoices.public-payment":{"uri":"invoice\/{invoiceId}\/payment","methods":["GET","HEAD"],"parameters":["invoiceId"]},"admin.invoices.pdf":{"uri":"invoices-pdf","methods":["GET","HEAD"]},"admin.invoicesExcel":{"uri":"invoices-excel","methods":["GET","HEAD"]},"public-view-invoice.pdf":{"uri":"invoice-pdf\/{invoice}","methods":["GET","HEAD"],"parameters":["invoice"]},"admin.quotesExcel":{"uri":"quotes-excel","methods":["GET","HEAD"]},"admin.quotes.pdf":{"uri":"quotes-pdf","methods":["GET","HEAD"]},"quotes.pdf":{"uri":"quotes\/{quote}\/pdf","methods":["GET","HEAD"],"parameters":["quote"],"bindings":{"quote":"id"}},"admin.paymentsExcel":{"uri":"admin-payments-excel","methods":["GET","HEAD"]},"admin.payments.pdf":{"uri":"admin-payments-pdf","methods":["GET","HEAD"]},"admin.transactionsExcel":{"uri":"transactions-excel","methods":["GET","HEAD"]},"admin.export.transactions.pdf":{"uri":"transactions-pdf","methods":["GET","HEAD"]},"clients.payments.store":{"uri":"client\/payments","methods":["POST"]},"client.stripe-payment":{"uri":"client\/stripe-payment","methods":["POST"]},"razorpay.init":{"uri":"client\/razorpayonboard","methods":["GET","HEAD"]},"paypal.init":{"uri":"client\/paypal-onboard","methods":["GET","HEAD"]},"payment-success":{"uri":"client\/payment-success","methods":["GET","HEAD"]},"failed-payment":{"uri":"client\/failed-payment","methods":["GET","HEAD"]},"paypal.success":{"uri":"client\/paypal-payment-success","methods":["GET","HEAD"]},"paypal.failed":{"uri":"client\/paypal-payment-failed","methods":["GET","HEAD"]},"razorpay.success":{"uri":"client\/razorpay-payment-success","methods":["POST"]},"razorpay.failed":{"uri":"client\/razorpay-payment-failed","methods":["POST"]},"razorpay.webhook":{"uri":"client\/razorpay-payment-webhook","methods":["GET","HEAD"]},"client.paystack.init":{"uri":"client\/paystack-onboard","methods":["GET","HEAD"]},"client.paystack.success":{"uri":"client\/paystack-payment-success","methods":["GET","HEAD","POST","PUT","PATCH","DELETE","OPTIONS"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
